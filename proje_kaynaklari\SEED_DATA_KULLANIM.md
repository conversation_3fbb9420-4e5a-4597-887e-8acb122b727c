# 🌱 KobiPanel Seed Data Kullanım Kılavuzu

## 📋 <PERSON>l Bakış

Bu script, KobiPanel SQLite veritabanına sahte test verilerini eklemek için oluşturulmuştur. Eski ASP.NET MVC projesindeki Configuration.cs dosyasından dönüştürülmüştür.

## 🚀 Kurulum

### 1. <PERSON>erekli <PERSON>
```bash
pip install faker
```

### 2. Script'i Çalıştır

#### Tam <PERSON>eri Seti (Önerilen)
```bash
python seed_data.py
```

#### Sadece Ad<PERSON>
```bash
python seed_data.py --admin-only
```

## 📊 Eklenen Veriler

### 👥 Kullanıcılar (6 adet)
- **ismail** / şifre: `1jkdhdkj` (Yönetici)
- **aleyna** / şifre: `1jkdhdkj` (Kullanıcı)
- **teslime** / şifre: `4070` (Kullanıcı)
- **ahmet** / şifre: `3246` (Kullanıcı)
- **mustafa** / şifre: `4070` (Kullanıcı)
- **arda** / şifre: `123456` (Misafir)

### 🏙️ Adres Verileri
- **İller:** 81 adet (Türkiye'nin tüm illeri)
- **İlçeler:** 17 adet (Aydın ili)
- **Mahalleler:** 10 adet (Efeler ilçesi)

### 📦 Ürünler (6 adet)
- Büyük Mısır Silajı (350₺ alış / 400₺ satış)
- Küçük Mısır Silajı (180₺ alış / 200₺ satış)
- Mısır Sapı (220₺ alış / 250₺ satış)
- Arpa Silajı (300₺ alış / 350₺ satış)
- Buğday Silajı (280₺ alış / 320₺ satış)
- Yonca Silajı (400₺ alış / 450₺ satış)

### 👤 İş Verileri
- **Müşteriler:** 50 adet (rastgele Türkçe isimler)
- **Satışlar:** 100 adet (son 1 yıl)
- **Giderler:** 30 adet (son 1 yıl)
- **Notlar:** 20 adet (son 6 ay)

### 🚗 Araç Verileri
- **Araçlar:** 3 adet (Ford Transit, Mercedes Sprinter, Iveco Daily)
- **Yakıt Kayıtları:** 50 adet (son 6 ay)

### 🌾 Tarım Verileri
- **Biçilen Tarlalar:** 20 adet (son 3 ay)
- **Kantar Fişleri:** 30 adet (son 3 ay)

## 🔐 Güvenlik

- Tüm şifreler SHA256 ile hashlenmiştir
- Gerçek email adresleri kullanılmamıştır
- Test verileri gerçek kişi/kurum bilgileri içermez

## ⚠️ Önemli Notlar

1. **Veritabanı Temizliği:** Script çalıştırılmadan önce veritabanının boş olduğundan emin olun
2. **Foreign Key Constraints:** Script, doğru sırayla veri ekler (önce parent tablolar)
3. **Tarih Aralıkları:** Veriler mantıklı tarih aralıklarında oluşturulur
4. **Stok Miktarları:** Ürünler yeterli stok miktarı ile oluşturulur

## 🛠️ Özelleştirme

### Veri Miktarlarını Değiştirme
```python
# seed_data.py dosyasında bu değerleri değiştirebilirsiniz:
for i in range(50):  # Müşteri sayısı
for i in range(100): # Satış sayısı
for i in range(30):  # Gider sayısı
```

### Yeni Kullanıcı Ekleme
```python
users_data = [
    ("kullanici_adi", hash_password("sifre"), "Ad Soyad", "<EMAIL>", "telefon", "rol", True),
    # Yeni kullanıcı buraya eklenebilir
]
```

## 🔧 Sorun Giderme

### Hata: "table doesn't exist"
- Önce veritabanı şemasının oluşturulduğundan emin olun
- Alembic migration'ları çalıştırın

### Hata: "UNIQUE constraint failed"
- Veritabanını temizleyin veya yeni bir veritabanı dosyası kullanın

### Hata: "faker module not found"
- `pip install faker` komutunu çalıştırın

## 📝 Changelog

### v1.0.0
- ASP.NET MVC Configuration.cs'den dönüştürüldü
- SQLite uyumlu hale getirildi
- Türkçe locale desteği eklendi
- Kapsamlı test verileri eklendi

## 🤝 Katkıda Bulunma

Yeni veri türleri eklemek veya mevcut verileri iyileştirmek için:
1. `seed_data.py` dosyasını düzenleyin
2. Yeni veri ekleme fonksiyonunu yazın
3. Ana fonksiyonda çağırın
4. Test edin

# Seed Data Script için gerekli kütüphaneler
faker==20.1.0

## 📞 Destek

Sorunlar için proje dokümantasyonunu kontrol edin veya geliştirici ile iletişime geçin.
