from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_
from app.db.database import get_db
from app.models.models import <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>halle, User
from app.schemas.customer import Customer as CustomerSchema, CustomerCreate, CustomerUpdate, CustomerList
from app.core.deps import get_current_active_user

router = APIRouter()

@router.get("/", response_model=List[CustomerList])
def read_customers(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = Query(None, description="Ad soyad veya telefon ile arama"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Müşteri listesini getir
    """
    query = db.query(Customer)
    
    # Arama filtresi
    if search:
        query = query.filter(
            or_(
                Customer.ad_soyad.contains(search),
                Customer.telefon.contains(search)
            )
        )
    
    customers = query.offset(skip).limit(limit).all()
    
    # Liste için özel format
    result = []
    for customer in customers:
        customer_data = {
            "id": customer.id,
            "ad_soyad": customer.ad_soyad,
            "telefon": customer.telefon,
            "kayit_tarihi": customer.kayit_tarihi,
            "il_adi": customer.il.ad if customer.il else None,
            "ilce_adi": customer.ilce.ad if customer.ilce else None,
            "mahalle_adi": customer.mahalle.ad if customer.mahalle else None,
        }
        result.append(customer_data)
    
    return result

@router.post("/", response_model=CustomerSchema)
def create_customer(
    customer: CustomerCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Yeni müşteri oluştur
    """
    db_customer = Customer(**customer.dict())
    db.add(db_customer)
    db.commit()
    db.refresh(db_customer)
    return db_customer

@router.get("/{customer_id}", response_model=CustomerSchema)
def read_customer(
    customer_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Müşteri detayını getir
    """
    customer = db.query(Customer).filter(Customer.id == customer_id).first()
    if customer is None:
        raise HTTPException(status_code=404, detail="Müşteri bulunamadı")
    return customer

@router.put("/{customer_id}", response_model=CustomerSchema)
def update_customer(
    customer_id: int,
    customer: CustomerUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Müşteri bilgilerini güncelle
    """
    db_customer = db.query(Customer).filter(Customer.id == customer_id).first()
    if db_customer is None:
        raise HTTPException(status_code=404, detail="Müşteri bulunamadı")
    
    # Güncelleme verilerini uygula
    update_data = customer.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_customer, field, value)
    
    db.commit()
    db.refresh(db_customer)
    return db_customer

@router.delete("/{customer_id}")
def delete_customer(
    customer_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Müşteriyi sil
    """
    db_customer = db.query(Customer).filter(Customer.id == customer_id).first()
    if db_customer is None:
        raise HTTPException(status_code=404, detail="Müşteri bulunamadı")
    
    # TODO: Müşteriye ait satışlar varsa silme işlemini engelle
    # Bu kontrol sonraki aşamada satış modeli eklendikten sonra yapılacak
    
    db.delete(db_customer)
    db.commit()
    return {"message": "Müşteri başarıyla silindi"}
