from typing import Generator, Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.core.security import verify_token
from app.models.models import User

security = HTTPBearer()

def get_current_user(
    db: Session = Depends(get_db),
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """
    Mevcut kullanıcıyı token'dan al
    """
    token = credentials.credentials
    username = verify_token(token)
    
    if username is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token geçersiz",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user = db.query(User).filter(User.kullanici_adi == username).first()
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="<PERSON><PERSON><PERSON><PERSON><PERSON> bulunamadı",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.aktif:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Kullanıcı aktif değil"
        )
    
    return user

def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    Aktif kullanıcıyı al
    """
    return current_user

def get_admin_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    Admin yetkisi olan kullanıcıyı al
    """
    if current_user.rol != "Yönetici":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Yeterli yetki yok"
        )
    return current_user
