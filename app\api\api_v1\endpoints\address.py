from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.models.models import <PERSON>, <PERSON><PERSON>, <PERSON>halle, User
from app.schemas.address import Il as IlSchema, <PERSON><PERSON> as <PERSON>ceSchema, <PERSON><PERSON><PERSON> as MahalleSchema
from app.core.deps import get_current_active_user

router = APIRouter()

@router.get("/iller", response_model=List[IlSchema])
def get_iller(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Tüm illeri getir
    """
    iller = db.query(Il).order_by(Il.ad).all()
    return iller

@router.get("/ilceler/{il_id}", response_model=List[IlceSchema])
def get_ilceler(
    il_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Belirtilen ile ait ilçeleri getir
    """
    ilceler = db.query(Ilce).filter(Ilce.il_id == il_id).order_by(Ilce.ad).all()
    return ilceler

@router.get("/mahalleler/{ilce_id}", response_model=List[MahalleSchema])
def get_mahalleler(
    ilce_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Belirtilen ilçeye ait mahalleleri getir
    """
    mahalleler = db.query(Mahalle).filter(Mahalle.ilce_id == ilce_id).order_by(Mahalle.ad).all()
    return mahalleler

# Admin endpoint'leri - İl/İlçe/Mahalle ekleme
@router.post("/iller", response_model=IlSchema)
def create_il(
    il_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Yeni il ekle
    """
    if current_user.rol != "Yönetici":
        raise HTTPException(status_code=403, detail="Yeterli yetki yok")
    
    db_il = Il(ad=il_data["ad"])
    db.add(db_il)
    db.commit()
    db.refresh(db_il)
    return db_il

@router.post("/ilceler", response_model=IlceSchema)
def create_ilce(
    ilce_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Yeni ilçe ekle
    """
    if current_user.rol != "Yönetici":
        raise HTTPException(status_code=403, detail="Yeterli yetki yok")
    
    db_ilce = Ilce(ad=ilce_data["ad"], il_id=ilce_data["il_id"])
    db.add(db_ilce)
    db.commit()
    db.refresh(db_ilce)
    return db_ilce

@router.post("/mahalleler", response_model=MahalleSchema)
def create_mahalle(
    mahalle_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Yeni mahalle ekle
    """
    if current_user.rol != "Yönetici":
        raise HTTPException(status_code=403, detail="Yeterli yetki yok")
    
    db_mahalle = Mahalle(ad=mahalle_data["ad"], ilce_id=mahalle_data["ilce_id"])
    db.add(db_mahalle)
    db.commit()
    db.refresh(db_mahalle)
    return db_mahalle
