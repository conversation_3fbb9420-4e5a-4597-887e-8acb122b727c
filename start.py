#!/usr/bin/env python3
"""
KobiPanel Backend Başlangıç Scripti
"""
import uvicorn
from app.main import app
from app.db.init_data import init_database

if __name__ == "__main__":
    print("KobiPanel Backend başlatılıyor...")
    
    # Veritabanını başlangıç verileri ile doldur
    print("Veritabanı başlangıç verileri kontrol ediliyor...")
    init_database()
    
    # FastAPI uygulamasını başlat
    print("FastAPI sunucusu başlatılıyor...")
    print("API Dokümantasyonu: http://localhost:8000/docs")
    print("Admin Kullanıcı: admin / admin123")
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
