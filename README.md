# KobiPanel Backend API

Aydın Silaj Yönetim Sistemi Backend API - FastAPI ile geliştirilmiştir.

## Kurulum

### 1. Virtual Environment Oluştur
```bash
python -m venv venv
```

### 2. Virtual Environment'ı Aktifleştir
```bash
# Windows
venv\Scripts\activate

# Linux/Mac
source venv/bin/activate
```

### 3. Bağımlılıkları Yükle
```bash
pip install -r requirements.txt
```

### 4. Uygulamayı Başlat
```bash
python start.py
```

## API Dokümantasyonu

Uygulama başladıktan sonra aşağıdaki URL'lerden API dokümantasyonuna erişebilirsiniz:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## Varsayılan <PERSON>ı

- **Kullanıcı Adı**: admin
- **<PERSON>ifre**: admin123
- **Rol**: Yönetici

## API Endpoints

### Authentication
- `POST /api/v1/auth/login` - Kullanıcı girişi
- `POST /api/v1/auth/logout` - Kullanıcı çıkışı
- `GET /api/v1/auth/me` - Mevcut kullanıcı bilgileri

### Kullanıcı Yönetimi
- `GET /api/v1/users/` - Kullanıcı listesi
- `POST /api/v1/users/` - Yeni kullanıcı oluştur
- `GET /api/v1/users/{user_id}` - Kullanıcı detayı
- `PUT /api/v1/users/{user_id}` - Kullanıcı güncelle
- `DELETE /api/v1/users/{user_id}` - Kullanıcı sil
- `POST /api/v1/users/{user_id}/change-password` - Şifre değiştir

### Adres Yönetimi
- `GET /api/v1/address/iller` - İl listesi
- `GET /api/v1/address/ilceler/{il_id}` - İlçe listesi
- `GET /api/v1/address/mahalleler/{ilce_id}` - Mahalle listesi

### Müşteri Yönetimi
- `GET /api/v1/customers/` - Müşteri listesi
- `POST /api/v1/customers/` - Yeni müşteri oluştur
- `GET /api/v1/customers/{customer_id}` - Müşteri detayı
- `PUT /api/v1/customers/{customer_id}` - Müşteri güncelle
- `DELETE /api/v1/customers/{customer_id}` - Müşteri sil

### Ürün Yönetimi
- `GET /api/v1/products/` - Ürün listesi
- `POST /api/v1/products/` - Yeni ürün oluştur
- `GET /api/v1/products/{product_id}` - Ürün detayı
- `PUT /api/v1/products/{product_id}` - Ürün güncelle
- `DELETE /api/v1/products/{product_id}` - Ürün sil
- `GET /api/v1/products/stok/dusuk` - Stoku düşük ürünler

## Veritabanı

Proje SQLite veritabanı kullanmaktadır. Veritabanı dosyası `kobipanel.db` olarak oluşturulur.

## Güvenlik

- JWT token tabanlı authentication
- Bcrypt ile şifre hashleme
- Role-based access control (RBAC)
- CORS koruması

## Geliştirme

### Yeni Model Ekleme
1. `app/models/models.py` dosyasına model ekle
2. `app/schemas/` klasörüne Pydantic şemaları ekle
3. `app/api/api_v1/endpoints/` klasörüne endpoint'leri ekle
4. `app/api/api_v1/api.py` dosyasına router'ı dahil et

### Test Etme
```bash
# API test etmek için
curl -X POST "http://localhost:8000/api/v1/auth/login" \
     -H "Content-Type: application/json" \
     -d '{"kullanici_adi": "admin", "sifre": "admin123"}'
```

## Sonraki Aşamalar

Bu AŞAMA 1'de tamamlanan özellikler:
- ✅ FastAPI proje kurulumu
- ✅ SQLAlchemy modelleri (User, Customer, Product, Address)
- ✅ JWT Authentication sistemi
- ✅ Temel CRUD endpoints
- ✅ API dokümantasyonu

AŞAMA 2'de eklenecek özellikler:
- Satış yönetimi modülü
- Gider yönetimi modülü
- Not yönetimi modülü
- İş mantığı validasyonları
