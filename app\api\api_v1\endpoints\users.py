from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.core.security import get_password_hash, verify_password
from app.models.models import User
from app.schemas.user import User as UserSchema, UserCreate, UserUpdate, UserChangePassword
from app.core.deps import get_current_active_user, get_admin_user

router = APIRouter()

@router.get("/", response_model=List[UserSchema])
def read_users(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_user)
):
    """
    Kullanıcı listesini getir (Sadece admin)
    """
    users = db.query(User).offset(skip).limit(limit).all()
    return users

@router.post("/", response_model=UserSchema)
def create_user(
    user: UserCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_user)
):
    """
    Yeni kullanıcı oluştur (Sadece admin)
    """
    # Kullanıcı adı kontrolü
    db_user = db.query(User).filter(User.kullanici_adi == user.kullanici_adi).first()
    if db_user:
        raise HTTPException(
            status_code=400,
            detail="Bu kullanıcı adı zaten kullanılıyor"
        )
    
    # Email kontrolü
    if user.email:
        db_user = db.query(User).filter(User.email == user.email).first()
        if db_user:
            raise HTTPException(
                status_code=400,
                detail="Bu email adresi zaten kullanılıyor"
            )
    
    # Şifreyi hashle
    hashed_password = get_password_hash(user.sifre)
    
    db_user = User(
        kullanici_adi=user.kullanici_adi,
        sifre=hashed_password,
        ad_soyad=user.ad_soyad,
        email=user.email,
        telefon=user.telefon,
        rol=user.rol,
        aktif=user.aktif,
        profil_resmi=user.profil_resmi,
        aciklama=user.aciklama
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

@router.get("/{user_id}", response_model=UserSchema)
def read_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Kullanıcı detayını getir
    """
    # Sadece admin veya kendi bilgilerini görebilir
    if current_user.rol != "Yönetici" and current_user.id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Bu bilgilere erişim yetkiniz yok"
        )
    
    db_user = db.query(User).filter(User.id == user_id).first()
    if db_user is None:
        raise HTTPException(status_code=404, detail="Kullanıcı bulunamadı")
    return db_user

@router.put("/{user_id}", response_model=UserSchema)
def update_user(
    user_id: int,
    user: UserUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Kullanıcı bilgilerini güncelle
    """
    # Sadece admin veya kendi bilgilerini güncelleyebilir
    if current_user.rol != "Yönetici" and current_user.id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Bu bilgileri güncelleme yetkiniz yok"
        )
    
    db_user = db.query(User).filter(User.id == user_id).first()
    if db_user is None:
        raise HTTPException(status_code=404, detail="Kullanıcı bulunamadı")
    
    # Güncelleme verilerini uygula
    update_data = user.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_user, field, value)
    
    db.commit()
    db.refresh(db_user)
    return db_user

@router.delete("/{user_id}")
def delete_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_user)
):
    """
    Kullanıcıyı sil (Sadece admin)
    """
    if current_user.id == user_id:
        raise HTTPException(
            status_code=400,
            detail="Kendi hesabınızı silemezsiniz"
        )
    
    db_user = db.query(User).filter(User.id == user_id).first()
    if db_user is None:
        raise HTTPException(status_code=404, detail="Kullanıcı bulunamadı")
    
    db.delete(db_user)
    db.commit()
    return {"message": "Kullanıcı başarıyla silindi"}

@router.post("/{user_id}/change-password")
def change_password(
    user_id: int,
    password_data: UserChangePassword,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Kullanıcı şifresini değiştir
    """
    # Sadece kendi şifresini değiştirebilir
    if current_user.id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Sadece kendi şifrenizi değiştirebilirsiniz"
        )
    
    # Eski şifre kontrolü
    if not verify_password(password_data.eski_sifre, current_user.sifre):
        raise HTTPException(
            status_code=400,
            detail="Eski şifre hatalı"
        )
    
    # Yeni şifreyi hashle ve güncelle
    hashed_password = get_password_hash(password_data.yeni_sifre)
    current_user.sifre = hashed_password
    
    db.commit()
    return {"message": "Şifre başarıyla değiştirildi"}
